# Caption Creator

Please do coding of a Python program which attaches a caption in the designated color and font for a designated photo according to the following processes.  All the Windows opened in this program should have a whitish pale purple background, while text boxes should have a white background.

1. In the beginning, show a "Starter Window" for about 10 seconds, which shows (1) the program name "Caption Creator" in a big fancy font and (2) comments in the 10 point font "Coded by Copilot as directed by <PERSON><PERSON>, August, 2025." in the right lower location.
2. After the "Starter Window" as in 1. above disappears automatically in about 10 seconds, a new Window named "Setup Window" shows up to set up 3 things., A), B) and C) below.  
Included from the beginning in this Window are a), b), c) and d) below.  
a) A text box, "Enter your name" and two related buttons, "Save the name" and "Use the saved name".  
b) A text box, "Select the font" and two related buttons, "Save the font" and "Use the saved font".  
c) A colored box, "Select the background color" and two related buttons, "Save the color" and "Use the saved color".  
d) A button, "Next".  

Following are the processes attached to those gadgets above.  
A) The text box "Enter your name" shows the value of a variable "Name", the initial value of which is blank.  The user may write or re-write the name into the text box and click the "Save the name" button to store the name in the variable "Name", or the user may click the button "Use the saved name" to use the name already stored in "Name" in the past.  
B) The text box "Select the font" shows the value of a variable "Font", the initial value of which is "MS Mincho".  The user may use the MS Windows protocol to select a font to re-write the text box and click the "Save the font" button to store the font in the variable "Font, or the user may click the button "Use the saved font" to use the font already stored in "Font" in the past.  
C) Show a colored box titled "Background color" shows the value of a variable "Color", the initial value of which is "white".  The user may use the MS Windows protocol to select a color to re-write the color of the box and click the "Save the color" button to store the color in the variable "Color", or the user may click the button "Use the saved color" to use the color already stored in "Color" in the past.  
D) Only when all the processes of A), B) and C) are completed, the bottom button named "Next" is activated.  When this button is clicked, the next process described in 3. below begins.  
3. A new Window "Define the caption" is shown, in which a text box "Caption" and 4 buttons, "Select a photo", "Next", "Finalize the caption" and "Start over again" work as described in A) and B) below.  Shown at 70% of the height of the Window, upper right, is the thumb nail image of the value of the variable "Photo", and a text box and buttons are located below this thumb nail image.  

A) The 1st button is "Select a photo".  Clicking it activates following 4 things;

   1. Use MS Windows protocol to select and read a file of a photo to store the photo image in a variable "Photo".  The original location of the photo is memorized for later use.  The length and width of the photo are used as follows;  
   2. Make a caption strip of the width equal to that of the photo, and of the height of 5% of the height of the photo.  Fill the caption strip with the color of the variable "Color".  
   3. Then the caption strip above is concatenated below the photo image in "Photo", and the concatenated image is stored back into the variable "Photo".
   4. The concatenated image in the variable "Photo" is shown in the Window.

B) A text box "Caption" and the second button "Finalize the caption" work as follows;

   1. The user writes a caption text in the text box "Caption".  
   2. As the caption text is written in the text box "Caption", the caption text is immediately displayed at real-time on the caption strip part of the concatenated image described in A) in the following way;  
    (1) The font selected and stored in the variable "Font" is used.  
    (2) The size of the font is selected to fit the height of the caption strip part of the concatenated image.  
    (3) The caption text in the text box "Caption" is written into the caption strip part of the concatenated image, leaving blank spaces of 2 Kanji characters width at the left end.  Namely, the caption text starts at the 3rd Kanji character position from the left.  
    (4) The value of the variable "Name" is written at the right end of the caption strip part of the concatenated image, leaving blank spaces of 2 Kanji characters width at the right end.  
    (5) The coding doesn’t care about overlapping of texts in (3) and (4) above on the caption strip, because the user is required to adjust the length of the caption text.  
   3. When the user is content with the result and click the button "Finalize the caption", the whole concatenated image, with the texts on the caption strip, is stored back into the memory location from which the photo was originally read out in A) above.  When the user is not content with the result, the user may click the button "Start over again".  In this case, storing the concatenated image back into the original location of the photo doesn’t take place as if there were no operation since 2. above.  When either of the button is clicked, the operation on the read out photo is finished.  

C) When the operation on the read out photo is finished, the program goes back to 2. above.
End of the request for coding
